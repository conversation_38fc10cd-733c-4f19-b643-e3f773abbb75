import time
import traceback
from typing import Optional, Union

import panel as pn
import param

from server.db import queries, tables
from server.predictions import EmbeddingPrediction
from server.utils.types import LongRunningJobStatus


class Page:
    def __init__(self) -> None:
        self.widget_point_id = pn.widgets.TextInput(placeholder="Enter point ID...", sizing_mode="stretch_width")
        self.widget_model_id = pn.widgets.TextInput(placeholder="Enter model ID...", sizing_mode="stretch_width")
        self.search_button = pn.widgets.Button(
            name="Execute Search", button_type="primary", sizing_mode="stretch_width"
        )

        # Sync URL parameters with widget values
        if pn.state.location is not None:
            pn.state.location.sync(self.widget_point_id, {"value": "point_id"})
            pn.state.location.sync(self.widget_model_id, {"value": "model_id"})

        self.search_results = pn.bind(self.search, self.search_button)

    def _get_point(self, point_id: str) -> Optional[Union[tables.LabelPoint, tables.PredictionPoint]]:
        point: Optional[Union[tables.LabelPoint, tables.PredictionPoint]] = None
        point = queries.prediction_points.get(point_id)
        if point is None:
            point = queries.label_points.get(point_id)

        return point

    def _get_model(self, model_id: str) -> Optional[tables.Model]:
        model: Optional[tables.Model] = queries.models.get(model_id)
        return model

    def _get_similar_points(self, model: tables.Model, embedding: list[float]) -> list[tables.LabelPoint]:
        label_points = queries.label_points.similarity_search(model, embedding)

        return label_points

    def search(self, compute: bool) -> pn.Column:
        try:
            point_id = self.widget_point_id.value.rstrip()
            model_id = self.widget_model_id.value.rstrip()

            if point_id == "":
                return pn.Column()

            if model_id == "":
                return pn.Column()

            point = self._get_point(point_id)
            model = self._get_model(model_id)

            if point is None:
                return pn.Column(pn.pane.Markdown(f"Point not found: {point_id}"))
            if model is None:
                return pn.Column(pn.pane.Markdown(f"Model not found: {model_id}"))

            if hasattr(point, "image_id"):
                image_id = point.image_id
            elif hasattr(point, "image") and hasattr(point.image, "id"):
                image_id = point.image.id
            elif (
                hasattr(point, "prediction")
                and hasattr(point.prediction, "image")
                and hasattr(point.prediction.image, "id")
            ):
                image_id = point.prediction.image.id
            else:
                return pn.Column(pn.pane.Markdown(f"Could not find image ID for point: {point_id}"))

            embedding_prediction = EmbeddingPrediction(
                model_id=model_id,
                image_id=image_id,
                x=point.x,
                y=point.y,
                interactive=True,
            )

            start_time = time.time()
            while embedding_prediction.status != LongRunningJobStatus.SUCCESS:
                time.sleep(0.1)
                if start_time + 10 < time.time():
                    return pn.Column(pn.pane.Markdown(f"Timeout waiting for embedding prediction"))

            if embedding_prediction.data is None:
                return pn.Column(pn.pane.Markdown(f"No embedding data returned"))

            embedding = embedding_prediction.data.embedding

            label_points = self._get_similar_points(model, embedding)

            return pn.Column(
                pn.pane.Markdown(f"**Point:** {point_id}"),
                pn.pane.Markdown(f"**Image ID:** {image_id}"),
                pn.pane.Markdown(f"**Position:** ({point.x}, {point.y})"),
                pn.pane.Markdown(f"**Embedding dimension:** {len(embedding)}"),
                pn.pane.Markdown(f"**Embedding preview:** {embedding[:5]}..."),
            )

        except Exception as e:
            stacktrace = traceback.format_exc()
            return pn.Column(pn.pane.Markdown(f"```\n{stacktrace}\n```"), sizing_mode="stretch_width")

    def render(self) -> pn.Column:
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")

        return pn.Column(
            pn.Spacer(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown("## Point ID"),
                    pn.Row(self.widget_point_id, self.widget_model_id, self.search_button, sizing_mode="stretch_width"),
                    self.search_results,
                    sizing_mode="stretch_width",
                    min_width=360,
                    align=("center", "start"),
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                align=("center", "center"),
            ),
        )


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )
