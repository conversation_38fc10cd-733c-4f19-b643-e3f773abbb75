from enum import Enum

# veselka run modes
MODE_DEVELOPMENT: str = "development"
MODE_ANONYMOUS: str = "anonymous"
MODE_TEST: str = "test"

# training types
MODEL_TYPE_PRETRAIN = "pretrain"
MODEL_TYPE_FULL_TRAIN = "full_train"
MODEL_TYPE_FINE_TUNE = "fine_tune"
MODEL_TYPE_GEO_FINE_TUNE = "geo_fine_tune"
MODEL_TYPE_DRIPTAPE = "driptape"
DEEPWEED_MODEL_SUBTYPES = {
    MODEL_TYPE_PRETRAIN,
    MODEL_TYPE_FINE_TUNE,
    MODEL_TYPE_FULL_TRAIN,
    M<PERSON>EL_TYPE_GEO_FINE_TUNE,
    MODEL_TYPE_DRIPTAPE,
}

MODEL_TYPE_DEEPWEED = "deepweed"
MODEL_TYPE_P2P = "p2p"
MODEL_TYPE_FURROW = "furrows"
MODEL_TYPE_FEWSHOT = "fewshot"
MODEL_TYPE_COMPARISON = "comparison"
MODEL_TYPE_SELFSUP = "selfsup"


class ModelType(str, Enum):
    DEEPWEED = MODEL_TYPE_DEEPWEED
    P2P = MODEL_TYPE_P2P
    FURROW = MODEL_TYPE_FURROW


GEOHASH_ZERO_POINT = "s00000000000"

RESERVATION_TIME = 15000000

OLDEST_PIPELINE_QUEUE_KEY = "oldest-pipeline"
OLDEST_PIPELINE_GEO_QUEUE_KEY = "oldest-pipeline-geo"
REDIS_7DAY_EXPIRATION = 7 * 24 * 60 * 60

DSV2_PAGE_SIZE_LIMIT = 8000

CARBON_ML_BUCKET = "carbon-ml"

REDIS_CACHE_EXPIRATION_SECONDS = 60 * 60 * 24 * 2


class GPUTypes(str, Enum):
    NVIDIA_RTX_A5000 = "NVIDIA-RTX-A5000"
    NVIDIA_RTX_ADA4000 = "NVIDIA-RTX-4000-Ada-Generation"


class TriggerType(str, Enum):
    OLDEST_PIPELINE = "oldest-pipeline"
    OLDEST_PIPELINE_GEO = "oldest-pipeline-geo"
    TIME_INTERVAL_HOURS = "time-interval-hours"
    TRAINABLE_IMAGE_INCREASE = "trainable-image-increase"
    TRAINABLE_IMAGE_INCREASE_VARIABLE_LIMIT = "trainable-image-increase-variable-limit"
    TRAINABLE_IMAGE_INCREASE_PER_GEOS = "trainable-image-increase-per-geos"
    TRAINABLE_IMAGE_INCREASE_PER_GEOS_VARIABLE_LIMIT = "trainable-image-increase-per-geos-variable-limit"
    TRAINABLE_IMAGE_INCREASE_PER_N_MODELS = "trainable-image-increase-per-n-models"
    TRAINABLE_IMAGE_INCREASE_PER_N_MODELS_GEOS = "trainable-image-increase-per-n-models-geos"
    ON_MODEL_POST = "on-model-post"


class TriggerService(str, Enum):
    JOB_CREATOR = "job-creator"
    MODEL_CONVERTER = "model-converter"


class NodeGroup(str, Enum):
    G8g256m64c = "G8g256m64c"
    G1g32m12c = "G1g32m12c"
    G8g512m64c = "G8g512m64c"


class PipelineCustomArgumentKey(str, Enum):
    DISABLE_CROP = "disable_crop"


class Environment(str, Enum):
    PRODUCTION = "production"
    PREVIEW = "preview"
    DEVELOPMENT = "development"


class TaskTypes(Enum):
    # Tasks for furrows
    FURROW_LABEL = "FURROW_LABEL"
    FURROW_REVIEW = "FURROW_REVIEW"


class ImageArtifactTypes(Enum):
    FURROW = "furrows"
    CHIP = "chip"
    PREDICT = "predict"
    CHIP_IMG = "chip_image"


class ImageArtifactSubtypes(Enum):
    RGB = "rgb"


class ClassificationType(Enum):
    POINT = "POINT"
    POLYGON = "POLYGON"
    LINE = "LINE"


class SortByColumn(Enum):
    CREATED = "CREATED"
    UPDATED = "UPDATED"
    CAPTURED_AT = "CAPTURED_AT"


class SortDirection(Enum):
    ASC = "ASC"
    DESC = "DESC"


class TaskState(Enum):
    IN_PROGRESS = "IN_PROGRESS"
    DONE = "DONE"
    INVALIDATED = "INVALIDATED"
    QUARANTINED = "QUARANTINED"


class QuarantineReason(Enum):
    UNFOCUSED = "UNFOCUSED"
    UNKNOWN_CROP = "UNKNOWN_CROP"
    CROP_CONFIG = "CROP_CONFIG"
    NOT_THINNING = "NOT_THINNING"
    OTHER = "OTHER"


class InvalidateReason(Enum):
    ADMIN = "ADMIN"
    OTHER = "OTHER"
    BLURRY_IMG = "BLURRY_IMG"
    DIRTY_LENS = "DIRTY_LENS"
    IMG_TOO_BRIGHT = "IMG_TOO_BRIGHT"
    IMG_TOO_DARK = "IMG_TOO_DARK"
    NO_CROPS_WEEDS = "NO_CROPS_WEEDS"
    WRONG_CROP = "WRONG_CROP"
    OVERCROWDED = "OVERCROWDED"
    TURN_AROUND = "TURN_AROUND"
    NO_LABELING_GUIDE = "NO_LABELING_GUIDE"


class SessionActivityState(Enum):
    ACTIVE = "ACTIVE"  # is still running
    INACTIVE = "INACTIVE"  # is not running anymore


class InvalidationReason(Enum):
    """Signifies why a task was invalidated"""

    Admin = 1
    Other = 2
    BlurryImage = 3
    DirtyLense = 4
    ImageTooBright = 5
    ImageTooDark = 6
    NoCropOrWeeds = 7
    WrongCrop = 8
    Overcrowded = 9
    Turnaround = 10
    NoLabelingGuide = 11

    @classmethod
    def validate(cls, reason: int) -> bool:
        valid_options = [
            cls.Admin.value,
            cls.Other.value,
            cls.BlurryImage.value,
            cls.DirtyLense.value,
            cls.ImageTooBright.value,
            cls.ImageTooDark.value,
            cls.NoCropOrWeeds.value,
            cls.WrongCrop.value,
            cls.Overcrowded.value,
            cls.Turnaround.value,
            cls.NoLabelingGuide.value,
        ]
        return reason in valid_options


class SupportedModelArtifact:
    def __init__(self, tensorrt_versions: list[str], compute_capability: str):
        self.tensorrt_versions = tensorrt_versions
        self.compute_capability = compute_capability


class ComputeCapability(Enum):
    AMPERE = "8.6"
    ADA = "8.9"


TENSORRT_VERSION_8_0_1_6 = "8.0.1.6"
TENSORRT_VERSION_10_0_1 = "10.0.1"


SUPPORTED_MODEL_ARTIFACTS = [
    SupportedModelArtifact([TENSORRT_VERSION_8_0_1_6, TENSORRT_VERSION_10_0_1], ComputeCapability.AMPERE.value),
    SupportedModelArtifact([TENSORRT_VERSION_8_0_1_6, TENSORRT_VERSION_10_0_1], ComputeCapability.ADA.value),
]

DEFAULT_WEED_CATEGORIES = ["BROADLEAF", "PURSLANE", "OFFSHOOT", "GRASS"]


CATEGORY_PROFILE_POINTS_TAG_NAME = "category_profile_points"


class Priority(str, Enum):
    NORMAL = "normal"
    EMERGENCY = "emergency"
