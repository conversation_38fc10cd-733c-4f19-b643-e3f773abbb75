from lib.common.s3_cache_proxy.client import S3CacheProxyClient
from lib.common.veselka.client import VeselkaClient
import os
from typing import Any, Optional, List
from PIL import Image
import pydantic
from deeplearning.self_supervised.encoders.resnet import ResNet
from deeplearning.self_supervised.models.SimpleContrastive import SimpleContrastiveModel
from deeplearning.self_supervised.datasets import ChipDataset, TwoChipDataset
import torchvision.transforms as TF
from scipy.ndimage import sobel
import torch
from deeplearning.scripts.utils.utils import get_dataset_v2
from deeplearning.constants import CARBON_DATA_DIR
import json
import tqdm
import logging

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)

class Config(pydantic.BaseModel):
    model_id: str
    num_epochs: int = 100
    num_samples: int = 10000
    num_validation_samples: int = 2000
    pretrained: bool = True
    num_workers: int = 2
    backbone: str = "resnet18"
    learning_rate: float = 1e-5
    description: Optional[str] = None
    model_type: str = "SimpleContrastive"
    wandb_project: str
    num_gpus: int
    fast_run: bool
    data_dir: str

def torch_sobel(img):
    h = sobel(img, axis=-1)
    v = sobel(img, axis=-2)

    return torch.sqrt(h**2 + v**2)


BASE_TRANSFORMS = [
    TF.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
    TF.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.5),
    TF.Grayscale(num_output_channels=3),
    TF.RandomHorizontalFlip(),
    TF.RandomVerticalFlip(),
    TF.RandomRotation(10),
]

MODEL_DATASET_MAP = {
    "SimpleContrastive": TwoChipDataset,
}


S3_CLIENT = S3CacheProxyClient(s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"))

def setup_logging(level: int = logging.INFO, format: str = "%(asctime)s - %(name)s - %(message)s") -> None:
    # Remove any existing handlers.
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Reconfigure logging.
    logging.basicConfig(level=level, format=format)

def s3_cache_load_fn(info: Any) -> Image.Image:
    uri = info["uri"]
    x = info["x"]
    y = info["y"]
    radius = info["radius"]
    bucket, key = S3_CLIENT.split_uri(uri)
    return S3_CLIENT._get_image_subset_from_s3_cache_proxy(
        bucket = bucket,
        key = key,
        x = x,
        y = y,
        radius = radius,
        fill = True,
    )

def get_model(config: Config):
    if config.backbone.lower().startswith("resnet"):
        encoder = ResNet(architecture = config.backbone, pretrained = config.pretrained, final_relu = False)
    else:
        raise ValueError(f"Unknown backbone: {config.backbone}")


    if config.model_type.lower() == "SimpleContrastive".lower():
        model = SimpleContrastiveModel(encoder = encoder, transforms = BASE_TRANSFORMS, loss_fn = "triplet")
    else:
        raise ValueError(f"Unknown model type: {config.model_type}")

    return model

def get_datasets(dataset_id: str, pipeline_id: str, config) -> List[Any]:
    size_limit = 1000 if config.fast_run else None
    LOG.info("Fetching dataset...")
    dataset_id, _ = get_dataset_v2(dataset_id, pipeline_id,
        train_set_size_limit = size_limit, validation_set_size_limit = size_limit, test_set_size_limit = size_limit,)
    
    LOG.info(f"Dataset ID: {dataset_id}")
    datasets = []

    for split in ["train", "validation", "test"]:
        LOG.info(f"Processing dataset split: {split}")
        assert split is not None, f"Dataset split {split} does not exist"
        split_json_path = os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{split}.jsonl")
        assert os.path.exists(split_json_path), f"Dataset split {split_json_path} does not exist"

        data = []

        with open(split_json_path) as f:
            for line in tqdm.tqdm(f):
                image = json.loads(line)

                for point in image["points"]:
                    
                    if point["radius"] == 0:
                        continue
                    
                    info = {
                        "uri": image["uri"],
                        "x": point["x"],
                        "y": point["y"],
                        "radius": point["radius"],
                    }

                    data.append(info)

        dataset = MODEL_DATASET_MAP[config.model_type](data, s3_cache_load_fn)

        datasets.append(dataset)

    return datasets
