import torch
import torchvision
import logging
from PIL import Image
from torch.distributed.launcher.api import LaunchConfig

IMAGENET_MEAN = [0.485, 0.456, 0.406]
IMAGENET_STD = [0.229, 0.224, 0.225]


def default_transform_fn(image: Image.Image) -> torch.Tensor:
    tensor_image: torch.Tensor
    tensor_image = torchvision.transforms.functional.to_tensor(image)
    tensor_image = torchvision.transforms.functional.resize(tensor_image, (224, 224), antialias=None)
    # tensor_image = torchvision.transforms.functional.normalize(tensor_image, IMAGENET_MEAN, IMAGENET_STD)

    if torch.isnan(tensor_image).any():
        logging.info(f"Tensor image has nan! {image.filename}")

    return tensor_image


def default_load_fn(filepath: str) -> Image.Image:
    with Image.open(filepath) as image:
        image = image.convert("RGB")

    return image


def get_elastic_launcher_config(num_gpus: int = 8) -> LaunchConfig:
    launch_config = LaunchConfig(
        min_nodes=1,
        max_nodes=1,
        nproc_per_node=num_gpus,
        run_id="none",
        role="default",
        rdzv_endpoint="127.0.0.1:29500",
        rdzv_backend="static",
        rdzv_configs={"rank": 0, "timeout": 900},
        rdzv_timeout=-1,
        max_restarts=0,
        monitor_interval=5,
        start_method="spawn",
        metrics_cfg={},
        local_addr=None,
    )
    return launch_config
