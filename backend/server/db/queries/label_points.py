from datetime import datetime
from typing import Dict, List, Optional, TypedDict

import pydantic
import sqlalchemy
from sqlalchemy import func
from sqlalchemy.orm.attributes import InstrumentedAttribute
from sqlalchemy.sql.expression import BinaryExpression, and_, or_

from server.constants import SortByColumn
from server.db import get_session
from server.db.queries.common_types import PaginationInput, QueryStats
from server.db.queries.helpers.query_helpers import add_pagination_to_query, calculate_query_stats
from server.db.queries.images import build_filter_conditions as build_image_filter_conditions
from server.db.queries.images_types import ImageFilterV2
from server.db.queries.label_points_types import Confidence, LabelPointsFilters
from server.db.queries.labels import LabelFilters
from server.db.queries.labels import build_filter_conditions as build_label_filter_conditions
from server.db.tables import Embedding, Image, Label, LabelPoint, Model, Task
from server.db.utils import PaginationCount, datetime_to_ms, get_count, session_scope
from server.utils.helpers import IsEmptyCheckPydanticMixin

DEFAULT_SORT_MAPPING: Dict[SortByColumn, InstrumentedAttribute] = {
    SortByColumn.CREATED: LabelPoint.created,
    SortByColumn.UPDATED: LabelPoint.updated,
    SortByColumn.CAPTURED_AT: Image.captured_at,
}


class PredictionMetadata(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    x: float
    y: float
    radius: float
    point_category_id: Optional[str] = None
    model_id: str
    confidence: int = 0


def create_label_point(
    prediction_metadata: PredictionMetadata, image_id: str, label: Optional[Label] = None, do_commit: bool = True
) -> LabelPoint:
    with session_scope(do_commit=do_commit) as session:
        label_point = LabelPoint(
            x=prediction_metadata.x,
            y=prediction_metadata.y,
            radius=prediction_metadata.radius,
            point_category_id=prediction_metadata.point_category_id,
            image_id=image_id,
            label_id=label.id if label else None,
            confidence=prediction_metadata.confidence,
        )
        session.add(label_point)
    return label_point


def similarity_search(model: Model, embedding: list[float], limit: int = 10) -> list[LabelPoint]:
    session = get_session()

    subquery = (
        session.query(Embedding.id)
        .filter(Embedding.model_id == model.id)
        .order_by(Embedding.data.cosine_distance(embedding).asc())
        .limit(limit)
    )

    query = session.query(LabelPoint).filter(
        LabelPoint.embeddings.any(Embedding.model_id == model.id)
        & LabelPoint.embeddings.any(Embedding.id.in_(subquery.subquery()))
    )

    results: list[LabelPoint] = query.all()

    return results


def get(label_point_id: str) -> Optional[LabelPoint]:
    session = get_session()
    result: Optional[LabelPoint] = session.query(LabelPoint).get(label_point_id)
    return result


def get_points(label_point_ids: List[str]) -> List[LabelPoint]:
    session = get_session()
    result: List[LabelPoint] = session.query(LabelPoint).filter(LabelPoint.id.in_(label_point_ids)).all()
    return result


def get_by_image_id(image_id: str) -> list[LabelPoint]:
    session = get_session()
    result: list[LabelPoint] = session.query(LabelPoint).filter(LabelPoint.image_id == image_id).all()
    return result


def get_closest_label_point_match(image_id: str, x: float, y: float) -> Optional[LabelPoint]:
    session = get_session()
    result: Optional[LabelPoint] = (
        session.query(LabelPoint)
        .filter(LabelPoint.image_id == image_id)
        .filter(LabelPoint.label.has(Label.workflow == Label.WORKFLOW_REVIEW))
        .filter(LabelPoint.label.has(Label.valid == 1))
        .order_by(func.pow(LabelPoint.x - x, 2) + func.pow(LabelPoint.y - y, 2))
        .first()
    )
    return result


def get_list(
    page: Optional[int] = None, page_size: Optional[int] = None, filters: Optional[LabelPointsFilters] = None
) -> list[LabelPoint]:
    query = _construct_query(page=page, page_size=page_size, filters=filters)
    results: list[LabelPoint] = query.all()

    return results


def count(filters: Optional[LabelPointsFilters] = None, precise_count_limit: int = 100) -> PaginationCount:
    query = _construct_query(filters=filters)
    count = get_count(query, precise_count_limit)
    return count


def _construct_query(
    page: Optional[int] = None,
    page_size: Optional[int] = None,
    filters: Optional[LabelPointsFilters] = None,
) -> sqlalchemy.orm.Query:
    session = get_session()

    query = (
        session.query(LabelPoint)
        .join(Label, LabelPoint.label_id == Label.id)
        .join(Image, LabelPoint.image_id == Image.id)
        .join(Task, Label.task == Task.id)
        .filter(Label.workflow == Label.WORKFLOW_REVIEW)
        .filter(Label.valid == 1)
        .filter(Task.valid == 1)
        .filter(LabelPoint.image_id.isnot(None))
    )

    if filters:
        if filters.label_point_ids:
            query = query.filter(LabelPoint.id.in_(filters.label_point_ids))

        if filters.quarantined:
            query = query.filter(Image.quarantined == filters.quarantined)

        if filters.crop_ids:
            query = query.filter(Image.crop_id.in_(filters.crop_ids))

        if filters.robot_ids:
            query = query.filter(Image.robot_id.in_(filters.robot_ids))

        if filters.point_category_ids:
            query = query.filter(LabelPoint.point_category_id.in_(filters.point_category_ids))

        if filters.labeled_at:
            label_begin_date: Optional[datetime] = None
            label_end_date: Optional[datetime] = None
            label_begin_date, label_end_date = filters.labeled_at
            if label_begin_date:
                query = query.filter(Label.done >= datetime_to_ms(label_begin_date))
            if label_end_date:
                query = query.filter(Label.done <= datetime_to_ms(label_end_date))

        if filters.captured_at:
            capture_begin_date: Optional[datetime] = None
            capture_end_date: Optional[datetime] = None
            capture_begin_date, capture_end_date = filters.captured_at
            if capture_begin_date:
                query = query.filter(Image.captured_at >= datetime_to_ms(capture_begin_date))
            if capture_end_date:
                query = query.filter(Image.captured_at <= datetime_to_ms(capture_end_date))

        if filters.confidence:
            query = query.filter(LabelPoint.confidence == filters.confidence.value)

        if filters.image_task_or_label_id:
            query_by_image_id = query.filter(LabelPoint.image_id == filters.image_task_or_label_id)
            query_by_label_id = query.filter(LabelPoint.label_id == filters.image_task_or_label_id)
            query_by_task_id = query.filter(Task.id == filters.image_task_or_label_id)
            query = query_by_image_id.union(query_by_label_id, query_by_task_id)

    query = query.order_by(Image.captured_at.desc())
    if page and page > 0 and page_size and page_size > 0:
        query = query.offset((page - 1) * page_size).limit(page_size)

    return query


class LabelPointsFilterV2(IsEmptyCheckPydanticMixin, pydantic.BaseModel):
    label_point_ids: Optional[list[str]] = None
    point_category_ids: Optional[list[str]] = None
    confidence: Optional[Confidence] = None


def build_filter_conditions(filters: LabelPointsFilterV2) -> BinaryExpression:
    filter_conditions: list[BinaryExpression] = []

    if filters.label_point_ids:
        filter_conditions.append(LabelPoint.id.in_(filters.label_point_ids))

    if filters.point_category_ids:
        filter_conditions.append(LabelPoint.point_category_id.in_(filters.point_category_ids))

    if filters.confidence:
        filter_conditions.append(LabelPoint.confidence == filters.confidence.value)

    return and_(*filter_conditions) if filter_conditions else True


class LabelPointsExploreFilter(IsEmptyCheckPydanticMixin, pydantic.BaseModel):
    image_filter: Optional[ImageFilterV2] = None
    label_point_filter: Optional[LabelPointsFilterV2] = None
    label_filter: Optional[LabelFilters] = None


class LabelPointsExploreResults(TypedDict):
    label_points: list[LabelPoint]
    query_stats: QueryStats


def explore_label_points(
    filter: LabelPointsExploreFilter, pagination_input: Optional[PaginationInput] = None, should_count: bool = False
) -> LabelPointsExploreResults:
    session = get_session()

    query = session.query(LabelPoint)

    if filter.image_filter and filter.image_filter.is_empty() is False:
        query = query.join(Image, LabelPoint.image_id == Image.id).filter(
            build_image_filter_conditions(filter.image_filter)
        )
    elif (
        pagination_input
        and pagination_input.sort_input
        and pagination_input.sort_input.sort_by == SortByColumn.CAPTURED_AT
    ):
        query = query.join(Image, LabelPoint.image_id == Image.id)
    else:
        query = query.join(Image, LabelPoint.image_id == Image.id)

    query = query.filter(or_(Image.image_type != "chip_image_large", Image.image_type.is_(None)))

    if filter.label_filter and filter.label_filter.is_empty() is False:
        query = query.join(Label, LabelPoint.label_id == Label.id).filter(
            build_label_filter_conditions(filter.label_filter)
        )

    if filter.label_point_filter and filter.label_point_filter.is_empty() is False:
        query = query.filter(build_filter_conditions(filter.label_point_filter))

    query_stats = calculate_query_stats(query, pagination_input, should_count)
    if pagination_input:
        query = add_pagination_to_query(query, pagination_input, DEFAULT_SORT_MAPPING)

    combined_results: list[LabelPoint] = query.all()
    return LabelPointsExploreResults(label_points=combined_results, query_stats=query_stats)
