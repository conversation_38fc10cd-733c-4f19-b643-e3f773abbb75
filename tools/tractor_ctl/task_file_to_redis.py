import argparse
import asyncio

from lib.common.redis_client import RedisClient
from tractor_ctl.constants import ACTIVE_TASK_KEY
from tractor_ctl.grpc.client import TractorCtlGrpcClient


async def load_task_from_file(fname: str, notify: bool) -> None:
    redis = await RedisClient.build(False)
    with open(fname, "rb") as fin:
        await redis.set(ACTIVE_TASK_KEY, fin.read())
    if notify:
        client = TractorCtlGrpcClient()
        await client.active_task_changed()


async def async_main() -> None:
    parser = argparse.ArgumentParser(description="Load task from file into redis")
    parser.add_argument("-n", "--notify", action="store_true", default=False, help="Notify tractor_ctl of change")
    parser.add_argument("fname", help="The file to read from", type=str)
    args = parser.parse_args()
    await load_task_from_file(args.fname, args.notify)


def main() -> None:
    asyncio.run(async_main())


if __name__ == "__main__":
    main()
