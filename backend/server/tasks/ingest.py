import json
import logging
import os
from typing import Optional

import h5py
import pydantic

from server import config, db, utils
from server.db import queries

from .app import app_task
from .constants import CeleryQueue

LOG = logging.getLogger(__name__)


class EmbeddingDatasetMetadata(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    model_id: str


class EmbeddingDatapointMetadata(pydantic.BaseModel):
    model_config = pydantic.ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    image_id: str
    point_id: Optional[str] = None


@app_task(queue=CeleryQueue.CELERY.value)
def ingest_embeddings(embddings_url: str) -> None:
    bucket, key = utils.split_s3_url(embddings_url)
    filepath = f"{config.DATA_DIR}/embeddings/{os.getpid()}.hdf5"

    os.makedirs(os.path.dirname(filepath), exist_ok=True)

    LOG.info(f"Downloading embedding dataset from S3: {embddings_url}")

    utils.s3_download_object(bucket, key, filepath)

    LOG.info(f"Downloaded embedding dataset to {filepath}")

    with h5py.File(filepath, "r", libver="latest") as f:
        metadata = EmbeddingDatasetMetadata(**json.loads(f.attrs["metadata"]))

        num_datapoints = f["embeddings"].shape[0]

        LOG.info(f"Found {num_datapoints} datapoints in embedding dataset. Beginning ingest...")

        for index in range(num_datapoints):
            embedding = f["embeddings"][index]
            embedding_metadata = EmbeddingDatapointMetadata(**json.loads(f["embeddings_metadata"][index][0]))

            if embedding_metadata.point_id is None:
                LOG.info(f"Skipping embedding with no point id ({index + 1} / {num_datapoints})")
                continue

            with db.utils.committer():
                queries.embeddings.create(
                    model_id=metadata.model_id, point_id=embedding_metadata.point_id, embedding=embedding.tolist()
                )

        LOG.info(f"Finished ingesting embedding dataset.")

    os.remove(filepath)
