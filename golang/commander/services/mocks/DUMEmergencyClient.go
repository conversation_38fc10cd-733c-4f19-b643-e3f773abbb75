// Code generated by mockery v2.27.1. DO NOT EDIT.

package mocks

import (
	data_upload_manager "github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	mock "github.com/stretchr/testify/mock"
)

// DUMEmergencyClient is an autogenerated mock type for the DUMEmergencyClient type
type DUMEmergencyClient struct {
	mock.Mock
}

// CompleteDataCaptureSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) CompleteDataCaptureSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetCaptureProgress provides a mock function with given fields:
func (_m *DUMEmergencyClient) GetCaptureProgress() (data_upload_manager.CaptureProgress, error) {
	ret := _m.Called()

	var r0 data_upload_manager.CaptureProgress
	var r1 error
	if rf, ok := ret.Get(0).(func() (data_upload_manager.CaptureProgress, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() data_upload_manager.CaptureProgress); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(data_upload_manager.CaptureProgress)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRegularCaptureStatus provides a mock function with given fields:
func (_m *DUMEmergencyClient) GetRegularCaptureStatus() (data_upload_manager.RegularCaptureStatus, error) {
	ret := _m.Called()

	var r0 data_upload_manager.RegularCaptureStatus
	var r1 error
	if rf, ok := ret.Get(0).(func() (data_upload_manager.RegularCaptureStatus, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() data_upload_manager.RegularCaptureStatus); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(data_upload_manager.RegularCaptureStatus)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSessions provides a mock function with given fields:
func (_m *DUMEmergencyClient) GetSessions() ([]data_upload_manager.Session, error) {
	ret := _m.Called()

	var r0 []data_upload_manager.Session
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]data_upload_manager.Session, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []data_upload_manager.Session); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]data_upload_manager.Session)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetUploadProgress provides a mock function with given fields:
func (_m *DUMEmergencyClient) GetUploadProgress() (data_upload_manager.UploadProgress, error) {
	ret := _m.Called()

	var r0 data_upload_manager.UploadProgress
	var r1 error
	if rf, ok := ret.Get(0).(func() (data_upload_manager.UploadProgress, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() data_upload_manager.UploadProgress); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(data_upload_manager.UploadProgress)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PauseBackgroundDataUploadSession provides a mock function with given fields: name
func (_m *DUMEmergencyClient) PauseBackgroundDataUploadSession(name string) error {
	ret := _m.Called(name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PauseDataCaptureSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) PauseDataCaptureSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PauseDataUploadSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) PauseDataUploadSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ResumeBackgroundDataUploadSession provides a mock function with given fields: name
func (_m *DUMEmergencyClient) ResumeBackgroundDataUploadSession(name string) error {
	ret := _m.Called(name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ResumeDataCaptureSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) ResumeDataCaptureSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ResumeDataUploadSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) ResumeDataUploadSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SnapImages provides a mock function with given fields: crop, cropID
func (_m *DUMEmergencyClient) SnapImages(crop string, cropID string) error {
	ret := _m.Called(crop, cropID)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(crop, cropID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartBackgroundDataUploadSession provides a mock function with given fields: method, name
func (_m *DUMEmergencyClient) StartBackgroundDataUploadSession(method string, name string) error {
	ret := _m.Called(method, name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(method, name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartDataCaptureSession provides a mock function with given fields: sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture
func (_m *DUMEmergencyClient) StartDataCaptureSession(sessionName string, captureRate float64, crop string, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool) error {
	ret := _m.Called(sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture)

	var r0 error
	if rf, ok := ret.Get(0).(func(string, float64, string, string, bool, uint32, string, bool) error); ok {
		r0 = rf(sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StartDataUploadSession provides a mock function with given fields: method
func (_m *DUMEmergencyClient) StartDataUploadSession(method string) error {
	ret := _m.Called(method)

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(method)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StopBackgroundDataUploadSession provides a mock function with given fields: name
func (_m *DUMEmergencyClient) StopBackgroundDataUploadSession(name string) error {
	ret := _m.Called(name)

	var r0 error
	if rf, ok := ret.Get(0).(func(string) error); ok {
		r0 = rf(name)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StopDataCaptureSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) StopDataCaptureSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// StopDataUploadSession provides a mock function with given fields:
func (_m *DUMEmergencyClient) StopDataUploadSession() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewDUMEmergencyClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewDUMEmergencyClient creates a new instance of DUMEmergencyClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewDUMEmergencyClient(t mockConstructorTestingTNewDUMEmergencyClient) *DUMEmergencyClient {
	mock := &DUMEmergencyClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
