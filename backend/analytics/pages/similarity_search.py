import os
import time
import traceback
from typing import Optional, Union

import panel as pn
from pages.category_profile_viewer.constants import MIN_CHIP_SIZE
from pages.category_profile_viewer.helpers import get_chip_presigned_url_with_retries

from server.db import queries, tables
from server.predictions import EmbeddingPrediction
from server.utils.types import LongRunningJobStatus

LOCAL_TEST = os.getenv("ANALYTICS_MODE", "production") == "development"


class LabelPointAdapter:
    """
    Adapter to make LabelPoint compatible with PredictionPoint-expecting helper functions.
    The helper functions only need x, y, radius properties and the image URL.
    """

    def __init__(self, label_point: tables.LabelPoint):
        self.id = label_point.id
        self.x = label_point.x
        self.y = label_point.y
        self.radius = label_point.radius
        self.label_point = label_point


class Page:
    def __init__(self) -> None:
        self.widget_point_id = pn.widgets.TextInput(placeholder="Enter point ID...", sizing_mode="stretch_width")
        self.widget_model_id = pn.widgets.TextInput(placeholder="Enter model ID...", sizing_mode="stretch_width")
        self.search_button = pn.widgets.Button(
            name="Execute Search", button_type="primary", sizing_mode="stretch_width"
        )

        # Sync URL parameters with widget values
        if pn.state.location is not None:
            pn.state.location.sync(self.widget_point_id, {"value": "point_id"})
            pn.state.location.sync(self.widget_model_id, {"value": "model_id"})

        self.search_results = pn.bind(self.search, self.search_button)

    def _get_point(self, point_id: str) -> Optional[Union[tables.LabelPoint, tables.PredictionPoint]]:
        point: Optional[Union[tables.LabelPoint, tables.PredictionPoint]] = None
        point = queries.prediction_points.get(point_id)
        if point is None:
            point = queries.label_points.get(point_id)

        return point

    def _get_model(self, model_id: str) -> Optional[tables.Model]:
        model: Optional[tables.Model] = queries.models.get(model_id)
        return model

    def _get_similar_points(self, model: tables.Model, embedding: list[float]) -> list[tables.LabelPoint]:
        label_points = queries.label_points.similarity_search(model, embedding)

        return label_points

    def _get_presigned_urls_for_label_points(self, label_points: list[tables.LabelPoint]) -> dict[str, str]:
        """
        Fetch presigned URLs for a list of label points.
        Returns a mapping of point ID to presigned URL.
        """
        presigned_urls = {}
        failed_points = []

        for label_point in label_points:
            try:
                # Check if the label point has valid image data
                if label_point.image and label_point.image.url:
                    # Use adapter to make LabelPoint compatible with the helper function
                    # Pass the image URL explicitly since LabelPoint has direct image relationship
                    adapter = LabelPointAdapter(label_point)
                    presigned_url = get_chip_presigned_url_with_retries(adapter, url=label_point.image.url)
                    presigned_urls[label_point.id] = presigned_url
                else:
                    # Track points without valid image data
                    failed_points.append(f"{label_point.id} (no image data)")
                    continue
            except Exception as e:
                # Track failed points with error details
                failed_points.append(f"{label_point.id} ({str(e)[:50]}...)")
                continue

        # Log summary of failures if any
        if failed_points:
            print(
                f"Failed to get presigned URLs for {len(failed_points)} points: {failed_points[:3]}{'...' if len(failed_points) > 3 else ''}"
            )

        return presigned_urls

    def search(self, compute: bool) -> pn.Column:
        try:
            point_id = self.widget_point_id.value.rstrip()
            model_id = self.widget_model_id.value.rstrip()

            if point_id == "":
                return pn.Column()

            if model_id == "":
                return pn.Column()

            point = self._get_point(point_id)
            model = self._get_model(model_id)

            if point is None:
                return pn.Column(pn.pane.Markdown(f"Point not found: {point_id}"))
            if model is None:
                return pn.Column(pn.pane.Markdown(f"Model not found: {model_id}"))

            if hasattr(point, "image_id"):
                image_id = point.image_id
            elif hasattr(point, "image") and hasattr(point.image, "id"):
                image_id = point.image.id
            elif (
                hasattr(point, "prediction")
                and hasattr(point.prediction, "image")
                and hasattr(point.prediction.image, "id")
            ):
                image_id = point.prediction.image.id
            else:
                return pn.Column(pn.pane.Markdown(f"Could not find image ID for point: {point_id}"))

            embedding_prediction = EmbeddingPrediction(
                model_id=model_id,
                image_id=image_id,
                x=point.x,
                y=point.y,
                interactive=True,
            )

            start_time = time.time()
            while embedding_prediction.status != LongRunningJobStatus.SUCCESS:
                time.sleep(0.1)
                if start_time + 10 < time.time():
                    return pn.Column(pn.pane.Markdown(f"Timeout waiting for embedding prediction"))

            if embedding_prediction.data is None:
                return pn.Column(pn.pane.Markdown(f"No embedding data returned"))

            embedding = embedding_prediction.data.embedding

            label_points = self._get_similar_points(model, embedding)

            # Fetch presigned URLs for the similar points
            presigned_urls = self._get_presigned_urls_for_label_points(label_points)

            # Create the results display
            results_components = [
                pn.pane.Markdown(f"**Query Point:** {point_id}"),
                pn.pane.Markdown(f"**Image ID:** {image_id}"),
                pn.pane.Markdown(f"**Position:** ({point.x}, {point.y})"),
                pn.pane.Markdown(f"**Embedding dimension:** {len(embedding)}"),
                pn.pane.Markdown(f"**Embedding preview:** {embedding[:5]}..."),
                pn.pane.Markdown(f"**Found {len(label_points)} similar points:**"),
            ]

            # Create image grid for similar points
            if presigned_urls:
                image_components = []
                for i, label_point in enumerate(label_points):
                    if label_point.id in presigned_urls:
                        # Create a column for each similar point with image and metadata
                        # Handle development vs production mode for images
                        if LOCAL_TEST:
                            image_component = pn.pane.Markdown(f"*Image URL: {presigned_urls[label_point.id]}*")
                        else:
                            image_component = pn.pane.Image(presigned_urls[label_point.id], width=200, height=200)

                        point_info = pn.Column(
                            pn.pane.Markdown(f"**#{i+1} - Point ID:** {label_point.id}"),
                            pn.pane.Markdown(f"**Position:** ({label_point.x:.1f}, {label_point.y:.1f})"),
                            pn.pane.Markdown(f"**Radius:** {label_point.radius:.1f}"),
                            image_component,
                            width=220,
                            margin=(10, 10),
                        )
                        image_components.append(point_info)

                # Arrange images in a responsive grid (3 columns max)
                if image_components:
                    # Group images into rows of 3
                    rows = []
                    for i in range(0, len(image_components), 3):
                        row_components = image_components[i : i + 3]
                        rows.append(pn.Row(*row_components, sizing_mode="stretch_width"))

                    results_components.extend(rows)
                else:
                    results_components.append(pn.pane.Markdown("*No images could be loaded for the similar points.*"))
            else:
                results_components.append(pn.pane.Markdown("*No similar points with valid images found.*"))

            return pn.Column(*results_components, sizing_mode="stretch_width")

        except Exception as e:
            stacktrace = traceback.format_exc()
            return pn.Column(pn.pane.Markdown(f"```\n{stacktrace}\n```"), sizing_mode="stretch_width")

    def render(self) -> pn.Column:
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")

        return pn.Column(
            pn.Spacer(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown("## Point ID"),
                    pn.Row(self.widget_point_id, self.widget_model_id, self.search_button, sizing_mode="stretch_width"),
                    self.search_results,
                    sizing_mode="stretch_width",
                    min_width=360,
                    align=("center", "start"),
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                align=("center", "center"),
            ),
        )


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )
