import time
import traceback
from typing import Optional, Union

import panel as pn
from pages.category_profile_viewer.helpers import get_chip_presigned_url_with_retries

from server.db import queries, tables
from server.predictions import EmbeddingPrediction
from server.utils.types import LongRunningJobStatus

IMAGE_SIZE = 250


class LabelPointAdapter:
    """
    Adapter to make LabelPoint compatible with PredictionPoint-expecting helper functions.
    The helper functions only need x, y, radius properties and the image URL.
    """

    def __init__(self, label_point: tables.LabelPoint):
        self.id = label_point.id
        self.x = label_point.x
        self.y = label_point.y
        self.radius = label_point.radius
        self.label_point = label_point


class Page:
    def __init__(self) -> None:
        self.widget_point_id = pn.widgets.TextInput(placeholder="Enter point ID...", sizing_mode="stretch_width")
        self.widget_model_id = pn.widgets.TextInput(placeholder="Enter model ID...", sizing_mode="stretch_width")
        self.search_button = pn.widgets.Button(
            name="Execute Search", button_type="primary", sizing_mode="stretch_width"
        )

        # Sync URL parameters with widget values
        if pn.state.location is not None:
            pn.state.location.sync(self.widget_point_id, {"value": "point_id"})
            pn.state.location.sync(self.widget_model_id, {"value": "model_id"})

        self.search_results = pn.bind(self.search, self.search_button)

    def _get_point(self, point_id: str) -> Optional[Union[tables.LabelPoint, tables.PredictionPoint]]:
        point: Optional[Union[tables.LabelPoint, tables.PredictionPoint]] = None
        point = queries.prediction_points.get(point_id)
        if point is None:
            point = queries.label_points.get(point_id)

        return point

    def _get_model(self, model_id: str) -> Optional[tables.Model]:
        model: Optional[tables.Model] = queries.models.get(model_id)
        return model

    def _get_similar_points(
        self, model: tables.Model, embedding: list[float]
    ) -> tuple[list[tables.LabelPoint], list[float]]:
        results = queries.label_points.similarity_search(model, embedding, limit=25)
        label_points = []
        distances = []

        for embedding, distance in results:
            label_points.append(embedding.point)
            distances.append(distance)

        return label_points, distances

    def _get_presigned_urls_for_label_points(self, label_points: list[tables.LabelPoint]) -> dict[str, str]:
        """
        Fetch presigned URLs for a list of label points.
        Returns a mapping of point ID to presigned URL.
        """
        presigned_urls = {}
        failed_points = []

        for label_point in label_points:
            try:
                # Check if the label point has valid image data
                if label_point.image and label_point.image.url:
                    # Use adapter to make LabelPoint compatible with the helper function
                    # Pass the image URL explicitly since LabelPoint has direct image relationship
                    adapter = LabelPointAdapter(label_point)
                    presigned_url = get_chip_presigned_url_with_retries(adapter, url=label_point.image.url)
                    presigned_urls[label_point.id] = presigned_url
                else:
                    # Track points without valid image data
                    failed_points.append(f"{label_point.id} (no image data)")
                    continue
            except Exception as e:
                # Track failed points with error details
                failed_points.append(f"{label_point.id} ({str(e)[:50]}...)")
                continue

        # Log summary of failures if any
        if failed_points:
            print(
                f"Failed to get presigned URLs for {len(failed_points)} points: {failed_points[:3]}{'...' if len(failed_points) > 3 else ''}"
            )

        return presigned_urls

    def search(self, compute: bool) -> pn.Column:
        if not compute:
            return pn.Column()

        try:
            point_id = self.widget_point_id.value.rstrip()
            model_id = self.widget_model_id.value.rstrip()

            if point_id == "":
                return pn.Column()

            if model_id == "":
                return pn.Column()

            point = self._get_point(point_id)
            model = self._get_model(model_id)

            if point is None:
                return pn.Column(pn.pane.Markdown(f"Point not found: {point_id}"))
            if model is None:
                return pn.Column(pn.pane.Markdown(f"Model not found: {model_id}"))

            if hasattr(point, "image_id"):
                image_id = point.image_id
            elif hasattr(point, "image") and hasattr(point.image, "id"):
                image_id = point.image.id
            elif (
                hasattr(point, "prediction")
                and hasattr(point.prediction, "image")
                and hasattr(point.prediction.image, "id")
            ):
                image_id = point.prediction.image.id
            else:
                return pn.Column(pn.pane.Markdown(f"Could not find image ID for point: {point_id}"))

            embedding_prediction = EmbeddingPrediction(
                model_id=model_id,
                image_id=image_id,
                x=point.x,
                y=point.y,
                interactive=True,
            )

            start_time = time.time()
            while embedding_prediction.status != LongRunningJobStatus.SUCCESS:
                time.sleep(0.1)
                if start_time + 10 < time.time():
                    return pn.Column(pn.pane.Markdown(f"Timeout waiting for embedding prediction"))

            if embedding_prediction.data is None:
                return pn.Column(pn.pane.Markdown(f"No embedding data returned"))

            label_points, distances = self._get_similar_points(model, embedding_prediction.data.embedding)
            presigned_urls = self._get_presigned_urls_for_label_points(label_points)

            rendered_results = []
            if presigned_urls:
                reference_image_url = None
                if hasattr(point, "image") and point.image and point.image.url:
                    reference_adapter = LabelPointAdapter(point)
                    reference_image_url = get_chip_presigned_url_with_retries(reference_adapter, url=point.image.url)
                elif (
                    hasattr(point, "prediction")
                    and point.prediction
                    and point.prediction.image
                    and point.prediction.image.url
                ):
                    reference_image_url = get_chip_presigned_url_with_retries(point, url=point.prediction.image.url)

                for i, label_point in enumerate(label_points):
                    if label_point.id in presigned_urls:
                        reference_image = pn.pane.Image(reference_image_url, width=IMAGE_SIZE, height=IMAGE_SIZE)
                        image = pn.pane.Image(presigned_urls[label_point.id], width=IMAGE_SIZE, height=IMAGE_SIZE)

                        similarity = 1 - distances[i] / 2.0
                        metadata = pn.Column(
                            pn.pane.Markdown(
                                "```\n"
                                + f"Point ID:   {label_point.id}\n"
                                + f"Image ID:   {label_point.image.id if label_point.image else 'N/A'}\n"
                                + f"Position:   ({label_point.x:.1f}, {label_point.y:.1f})\n"
                                + f"Radius:     {label_point.radius:.1f}\n"
                                + f"Similarity: {similarity:.3f}\n"
                                + "```",
                                sizing_mode="stretch_width",
                            ),
                            sizing_mode="stretch_width",
                        )

                        point_row = pn.Row(reference_image, image, metadata, sizing_mode="stretch_width")

                        rendered_results.append(point_row)

            return pn.Column(*rendered_results, sizing_mode="stretch_width")

        except Exception as e:
            stacktrace = traceback.format_exc()
            return pn.Column(pn.pane.Markdown(f"```\n{stacktrace}\n```"), sizing_mode="stretch_width")

    def render(self) -> pn.Column:
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")

        return pn.Column(
            pn.Spacer(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown("## Point ID"),
                    pn.Row(self.widget_point_id, self.widget_model_id, self.search_button, sizing_mode="stretch_width"),
                    self.search_results,
                    sizing_mode="stretch_width",
                    min_width=360,
                    align=("center", "start"),
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                align=("center", "center"),
            ),
        )


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )
