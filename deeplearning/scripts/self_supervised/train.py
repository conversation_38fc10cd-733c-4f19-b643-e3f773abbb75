import argparse
import logging
import json
from deeplearning.scripts.self_supervised.utils import Config, get_model, get_datasets, setup_logging
from deeplearning.self_supervised.train import train
from deeplearning.scripts.utils.utils import add_common_arguments, generate_model_id
from deeplearning.constants import CARBON_DATA_DIR

setup_logging(level=logging.INFO)
LOG = logging.getLogger(__name__)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    add_common_arguments(parser)
    args = parser.parse_args()

    model_id = args.job_id
    if model_id is None:
        model_id = generate_model_id()


    config_dict = {
        "num_epochs": 1 if args.fast_run else 100,
        "num_samples": 64 if args.fast_run else 10000,
        "num_validation_samples": 2 if args.fast_run else 2000,
        "wandb_project": "selfsup-fast-run" if args.fast_run else "selfsup",
        "num_gpus": args.nproc_per_node,
        "model_id": model_id,
        "description": args.description,
        "fast_run": args.fast_run,
        "data_dir": f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
        **args.dl_config
    }

    config = Config(**config_dict)
    LOG.info(f"Config:\n{json.dumps(config_dict, indent=4)}")
    train_dataset, val_dataset, _ = get_datasets(args.dataset_id, args.pipeline_id, config)

    LOG.info("Downloading Model...")
    model = get_model(config)

    train(model, train_dataset, val_dataset, **config.model_dump())




