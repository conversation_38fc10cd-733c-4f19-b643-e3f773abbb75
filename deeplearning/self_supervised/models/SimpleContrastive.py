import torchvision
from deeplearning.self_supervised.utilities import IMAGENET_MEAN, IMAGENET_STD

from typing import Any, Callable, List

import numpy as np
import torch

from ..models.base import BaseEncoder
from .base import BaseModel


class SimpleContrastiveModel(BaseModel):
    def __init__(
        self,
        encoder: BaseEncoder,
        transforms: List[Callable[[torch.Tensor], torch.Tensor]],
        loss_fn: str = "quadruplet",
        seed: int = 1000,
    ):
        super().__init__()

        self._transforms = transforms
        self._encoder = encoder
        self._rng = np.random.RandomState(seed)
        self._loss_fn = loss_fn

    @property
    def num_transforms(self) -> int:
        return len(self._transforms)

    def set_seed(self, seed: int):
        self._rng = np.random.RandomState(seed)

    def forward(self, x1: torch.Tensor, x2: torch.Tensor) -> torch.Tensor:

        transform_1 = self._transforms[self._rng.randint(self.num_transforms)]
        transform_2 = self._transforms[self._rng.randint(self.num_transforms)]

        x1_t = torch.stack([transform_1(x) for x in x1])
        x2_t = torch.stack([transform_2(x) for x in x2])

        normalize_fn = torchvision.transforms.functional.normalize

        x1 = torch.stack([normalize_fn(x, IMAGENET_MEAN, IMAGENET_STD) for x in x1])
        x2 = torch.stack([normalize_fn(x, IMAGENET_MEAN, IMAGENET_STD) for x in x2])
        x1_t = torch.stack([normalize_fn(x, IMAGENET_MEAN, IMAGENET_STD) for x in x1_t])
        x2_t = torch.stack([normalize_fn(x, IMAGENET_MEAN, IMAGENET_STD) for x in x2_t])

        if torch.isnan(x1).any():
            print("x1 has nan")

        if torch.isnan(x2).any():
            print("x2 has nan")

        if torch.isnan(x1_t).any():
            print("x1_t has nan")

        if torch.isnan(x2_t).any():
            print("x2_t has nan")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)
        emb_1 = emb_1 / emb_1.norm(dim=1, keepdim=True)
        emb_2 = emb_2 / emb_2.norm(dim=1, keepdim=True)

        emb_1_t = self._encoder(x1_t)
        emb_2_t = self._encoder(x2_t)
        emb_1_t = emb_1_t / emb_1_t.norm(dim=1, keepdim=True)
        emb_2_t = emb_2_t / emb_2_t.norm(dim=1, keepdim=True)

        if torch.isnan(emb_1).any():
            print("emb_1 has nan")

        if torch.isnan(emb_2).any():
            print("emb_2 has nan")

        if torch.isnan(emb_1_t).any():
            print("emb_1_t has nan")

        if torch.isnan(emb_2_t).any():
            print("emb_2_t has nan")

        if self._loss_fn == "quadruplet":
            return self.quadruplet_loss(emb_1, emb_2, emb_1_t, emb_2_t)
        elif self._loss_fn == "triplet":
            return torch.nn.functional.triplet_margin_loss(emb_1, emb_1_t, emb_2_t, margin=0.1)
        else:
            raise ValueError(f"Unknown loss function: {self._loss_fn}")

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> torch.Tensor:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        out: torch.Tensor = x1_norm @ x2_norm.t()
        return out

    def quadruplet_loss(
        self,
        emb_1: torch.Tensor,
        emb_2: torch.Tensor,
        emb_1_t: torch.Tensor,
        emb_2_t: torch.Tensor,
        margin: float = 1.0,
    ) -> torch.Tensor:
        emb1_pos = self.cosine_cdist(emb_1, emb_1_t)
        emb2_pos = self.cosine_cdist(emb_2, emb_2_t)

        emb1_neg = self.cosine_cdist(emb_1_t, emb_2_t)
        emb2_neg = self.cosine_cdist(emb_2_t, emb_1_t)

        l1 = torch.nn.ReLU()(emb1_pos - emb1_neg + margin)
        l2 = torch.nn.ReLU()(emb2_pos - emb2_neg + margin)
        loss: torch.Tensor = l1.mean() + l2.mean()
        return loss
